#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import csv
import os

def create_kuwait_iraq_relations_csv():
    """إنشاء ملف CSV يوضح تطور العلاقات الكويتية العراقية على مر السنين"""
    
    # البيانات - تطور العلاقات الكويتية العراقية
    data = [
        ["الفترة الزمنية", "طبيعة العلاقات", "الأحداث الرئيسية", "المجالات الاقتصادية", "العلاقات الدبلوماسية"],
        ["1961-1962", "متوترة", "استقلال الكويت ومطالبة العراق بضمها", "محدودة", "قطع العلاقات"],
        ["1963-1975", "متحسنة", "اعتراف العراق باستقلال الكويت", "تبادل تجاري محدود", "تبادل السفراء"],
        ["1976-1989", "إيجابية", "دعم الكويت للعراق في حربه مع إيران", "مساعدات مالية كويتية للعراق", "علاقات دبلوماسية كاملة"],
        ["1990-1991", "حرب", "غزو العراق للكويت", "توقف كامل للعلاقات الاقتصادية", "قطع العلاقات"],
        ["1991-2003", "عدائية", "فترة ما بعد حرب الخليج", "عقوبات اقتصادية", "غياب العلاقات الدبلوماسية"],
        ["2003-2010", "إعادة بناء", "سقوط نظام صدام حسين", "بداية التعاون الاقتصادي", "إعادة العلاقات الدبلوماسية"],
        ["2011-2018", "تحسن تدريجي", "زيارات متبادلة بين المسؤولين", "اتفاقيات تجارية واستثمارية", "تعزيز التمثيل الدبلوماسي"],
        ["2018-2023", "تعاون", "تنسيق مشترك في المجالات المختلفة", "مشاريع مشتركة واستثمارات متبادلة", "علاقات دبلوماسية قوية"],
        ["2023-الحاضر", "شراكة استراتيجية", "اتفاقيات تعاون شاملة", "تكامل اقتصادي وتجاري", "تنسيق السياسي على أعلى المستويات"]
    ]
    
    # إنشاء ملف CSV
    filename = "تطور_العلاقات_الكويتية_العراقية.csv"
    
    with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerows(data)
    
    print(f"تم إنشاء الملف بنجاح: {filename}")
    
    # إنشاء ملف تعليمات للتنسيق
    instructions_file = "تعليمات_تنسيق_العلاقات_الكويتية_العراقية.txt"
    with open(instructions_file, 'w', encoding='utf-8') as f:
        f.write("تعليمات تنسيق شيت العلاقات الكويتية العراقية:\n")
        f.write("=" * 50 + "\n\n")
        f.write("1. افتح الملف 'تطور_العلاقات_الكويتية_العراقية.csv' في Microsoft Excel\n\n")
        f.write("2. تنسيق العناوين:\n")
        f.write("   - لون خلفية الرأس: أزرق غامق\n")
        f.write("   - لون خط الرأس: أبيض\n")
        f.write("   - نوع الخط: عريض\n\n")
        f.write("3. تنسيق الفترات الزمنية حسب طبيعة العلاقات:\n")
        f.write("   - فترات التوتر والحرب: خلفية حمراء فاتحة\n")
        f.write("   - فترات التحسن: خلفية صفراء فاتحة\n")
        f.write("   - فترات التعاون والشراكة: خلفية خضراء فاتحة\n\n")
        f.write("4. إضافة تنسيق شرطي:\n")
        f.write("   - للعلاقات الدبلوماسية: لون أخضر للعلاقات الإيجابية، ولون أحمر للعلاقات السلبية\n\n")
        f.write("5. إضافة مخطط زمني:\n")
        f.write("   - إنشاء مخطط شريطي يوضح تطور العلاقات عبر الزمن\n\n")
        f.write("6. اضبط عرض الأعمدة حسب المحتوى\n\n")
        f.write("7. احفظ الملف بصيغة Excel (.xlsx)\n\n")
    
    print(f"تم إنشاء ملف التعليمات: {instructions_file}")
    
    return filename

if __name__ == "__main__":
    create_kuwait_iraq_relations_csv()